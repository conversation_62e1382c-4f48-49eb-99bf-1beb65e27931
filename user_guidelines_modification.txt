# User Guidelines 最小化修改指令

## 修改位置
在您现有User Guidelines配置中的 "Desktop Commander 强制使用规则" 部分，找到 "例外情况" 子节，如果没有则在该部分末尾添加。

## 需要添加的内容

在 "Desktop Commander 强制使用规则" 部分添加以下内容：

```
### 例外情况 (特殊操作)
- **文件内容编辑**: 不使用 edit_block_desktop-commander，改用 str-replace-editor 的 str_replace 命令
- **文件内容追加**: 不使用 write_file_desktop-commander 的 append 模式，改用 str-replace-editor 的 insert 命令

#### 基础工具使用方法
**str-replace-editor 编辑**:
- old_str_1: 要替换的原始文本
- new_str_1: 新的替换文本  
- old_str_start_line_number_1: 起始行号
- old_str_end_line_number_1: 结束行号

**str-replace-editor 追加**:
- insert_line_1: 插入位置行号（在此行后插入）
- new_str_1: 要插入的内容
- 追加到文件末尾：使用文件最后一行号作为insert_line_1
```

## 修改说明
- 只需要在现有配置中添加上述内容
- 不需要删除或修改其他任何部分
- 其他Desktop Commander功能保持不变
- 只影响编辑和追加这两个特定操作
